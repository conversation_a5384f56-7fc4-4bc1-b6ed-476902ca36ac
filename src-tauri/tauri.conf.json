{"$schema": "https://schema.tauri.app/config/2", "productName": "aipic", "version": "0.1.0", "identifier": "marvy.aipic.app", "build": {"beforeDevCommand": "pnpm dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "pnpm build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "aipic", "width": 800, "height": 600}], "security": {"csp": null}}, "plugins": {}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": ["../python-backend"]}}