
use std::process::Command;
use std::fs;
use std::env;
use serde::{Deserialize, Serialize};
use tauri::Manager;

#[derive(Debug, Serialize, Deserialize)]
pub struct ProcessingResult {
    success: bool,
    message: String,
    output_data: Option<String>,
}

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
async fn remove_background(app_handle: tauri::AppHandle, image_data: String) -> Result<ProcessingResult, String> {
    // Get the app's resource directory
    let resource_dir = app_handle.path().resource_dir()
        .map_err(|e| format!("Failed to get resource directory: {}", e))?;

    let python_backend_dir = resource_dir.join("python-backend");
    let python_script = python_backend_dir.join("remove_bg.py");

    // Check if Python script exists
    if !python_script.exists() {
        return Ok(ProcessingResult {
            success: false,
            message: "Python backend not found. Please ensure the application is properly installed.".to_string(),
            output_data: None,
        });
    }

    // Determine Python executable path
    let python_exe = if cfg!(windows) {
        python_backend_dir.join("venv").join("Scripts").join("python.exe")
    } else {
        python_backend_dir.join("venv").join("bin").join("python")
    };

    // Check if virtual environment exists
    if !python_exe.exists() {
        return Ok(ProcessingResult {
            success: false,
            message: "Python environment not set up. Please run the setup script first.".to_string(),
            output_data: None,
        });
    }

    // Create temporary files for input and output
    let temp_dir = env::temp_dir();
    let input_id = uuid::Uuid::new_v4().to_string();
    let input_file = temp_dir.join(format!("input_{}.txt", input_id));
    let output_file = temp_dir.join(format!("output_{}.txt", input_id));

    // Write base64 data to temporary file
    fs::write(&input_file, &image_data)
        .map_err(|e| format!("Failed to write input file: {}", e))?;

    // Execute Python script
    let output = Command::new(&python_exe)
        .arg(&python_script)
        .arg("--base64")
        .arg("--input")
        .arg(&input_file)
        .arg("--output")
        .arg(&output_file)
        .current_dir(&python_backend_dir)
        .output()
        .map_err(|e| format!("Failed to execute Python script: {}", e))?;

    // Clean up input file
    let _ = fs::remove_file(&input_file);

    if output.status.success() {
        // Read the result
        match fs::read_to_string(&output_file) {
            Ok(result_data) => {
                // Clean up output file
                let _ = fs::remove_file(&output_file);

                Ok(ProcessingResult {
                    success: true,
                    message: "Background removed successfully".to_string(),
                    output_data: Some(result_data),
                })
            }
            Err(e) => {
                let _ = fs::remove_file(&output_file);
                Ok(ProcessingResult {
                    success: false,
                    message: format!("Failed to read result: {}", e),
                    output_data: None,
                })
            }
        }
    } else {
        // Clean up output file
        let _ = fs::remove_file(&output_file);

        let error_msg = String::from_utf8_lossy(&output.stderr);
        Ok(ProcessingResult {
            success: false,
            message: format!("Python script failed: {}", error_msg),
            output_data: None,
        })
    }
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .invoke_handler(tauri::generate_handler![greet, remove_background])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
