
use std::process::Command;
use std::fs;
use std::env;
use serde::{Deserialize, Serialize};
use tauri::Manager;

#[derive(Debug, Serialize, Deserialize)]
pub struct ProcessingResult {
    success: bool,
    message: String,
    output_data: Option<String>,
}

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
async fn remove_background(app_handle: tauri::AppHandle, image_data: String) -> Result<ProcessingResult, String> {
    // Try to find Python backend directory
    // First try development mode (project root)
    let current_dir = env::current_dir().map_err(|e| format!("Failed to get current directory: {}", e))?;
    let dev_python_backend_dir = current_dir.join("python-backend");

    let (python_backend_dir, python_script, python_exe) = if dev_python_backend_dir.exists() {
        // Development mode
        let script = dev_python_backend_dir.join("remove_bg.py");
        let exe = if cfg!(windows) {
            dev_python_backend_dir.join("venv").join("Scripts").join("python.exe")
        } else {
            dev_python_backend_dir.join("venv").join("bin").join("python")
        };
        (dev_python_backend_dir, script, exe)
    } else {
        // Production mode - try resource directory
        let resource_dir = app_handle.path().resource_dir()
            .map_err(|e| format!("Failed to get resource directory: {}", e))?;
        let prod_python_backend_dir = resource_dir.join("python-backend");
        let script = prod_python_backend_dir.join("remove_bg.py");
        let exe = if cfg!(windows) {
            prod_python_backend_dir.join("venv").join("Scripts").join("python.exe")
        } else {
            prod_python_backend_dir.join("venv").join("bin").join("python")
        };
        (prod_python_backend_dir, script, exe)
    };

    // Check if Python script exists
    if !python_script.exists() {
        return Ok(ProcessingResult {
            success: false,
            message: format!("Python backend not found at: {}. Please ensure the application is properly installed.", python_script.display()),
            output_data: None,
        });
    }

    // Check if virtual environment exists
    if !python_exe.exists() {
        return Ok(ProcessingResult {
            success: false,
            message: format!("Python environment not found at: {}. Please run the setup script first.", python_exe.display()),
            output_data: None,
        });
    }

    // Create temporary files for input and output
    let temp_dir = env::temp_dir();
    let input_id = uuid::Uuid::new_v4().to_string();
    let input_file = temp_dir.join(format!("input_{}.txt", input_id));
    let output_file = temp_dir.join(format!("output_{}.txt", input_id));

    // Write base64 data to temporary file
    fs::write(&input_file, &image_data)
        .map_err(|e| format!("Failed to write input file: {}", e))?;

    // Execute Python script
    println!("Executing Python script: {:?}", python_exe);
    println!("Script path: {:?}", python_script);
    println!("Working directory: {:?}", python_backend_dir);

    let output = Command::new(&python_exe)
        .arg(&python_script)
        .arg("--base64")
        .arg("--input")
        .arg(&input_file)
        .arg("--output")
        .arg(&output_file)
        .current_dir(&python_backend_dir)
        .output()
        .map_err(|e| format!("Failed to execute Python script: {}", e))?;

    println!("Python script exit status: {}", output.status);
    if !output.stderr.is_empty() {
        println!("Python script stderr: {}", String::from_utf8_lossy(&output.stderr));
    }
    if !output.stdout.is_empty() {
        println!("Python script stdout: {}", String::from_utf8_lossy(&output.stdout));
    }

    // Clean up input file
    let _ = fs::remove_file(&input_file);

    if output.status.success() {
        // Read the result
        match fs::read_to_string(&output_file) {
            Ok(result_data) => {
                // Clean up output file
                let _ = fs::remove_file(&output_file);

                Ok(ProcessingResult {
                    success: true,
                    message: "Background removed successfully".to_string(),
                    output_data: Some(result_data),
                })
            }
            Err(e) => {
                let _ = fs::remove_file(&output_file);
                Ok(ProcessingResult {
                    success: false,
                    message: format!("Failed to read result: {}", e),
                    output_data: None,
                })
            }
        }
    } else {
        // Clean up output file
        let _ = fs::remove_file(&output_file);

        let error_msg = String::from_utf8_lossy(&output.stderr);
        Ok(ProcessingResult {
            success: false,
            message: format!("Python script failed: {}", error_msg),
            output_data: None,
        })
    }
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .invoke_handler(tauri::generate_handler![greet, remove_background])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
