import { TbHome } from "react-icons/tb";
import { css, cx } from "styled-system/css";
import { hstack } from "styled-system/patterns";

const sidebarStyle = css({
	position: "fixed",
	top: 0,
	left: 0,
	bottom: 0,
	width: 200,
	height: "100vh",
	backgroundColor: "white",
	overflow: "auto",
	borderRight: "1px solid #f1f1f1",
});

const sidebarItemStyle = hstack({
	padding: "10px",
	fontSize: "14px",
	cursor: "pointer",
	"&:hover": {
		backgroundColor: "#f1f1f1",
	},
});

const Sidebar = () => {
	return (
		<div className={sidebarStyle}>
			<div className={cx(sidebarItemStyle, css({ my: 4 }))}>PinkTool</div>
			<div className={sidebarItemStyle}>
				<TbHome size={22} className={css({ color: "gray.800" })} />
				Home
			</div>
		</div>
	);
};

export default Sidebar;
