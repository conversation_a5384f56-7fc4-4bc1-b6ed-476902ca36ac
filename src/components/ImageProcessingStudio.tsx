import { useState } from "react";
import { TbUpload, TbDownload, Tb<PERSON><PERSON><PERSON>, TbArrowUp, TbBackground, TbDroplet, TbLoader2 } from "react-icons/tb";
import { css, cx } from "styled-system/css";
import { hstack, grid } from "styled-system/patterns";
import { invoke } from "@tauri-apps/api/core";

const containerStyle = css({
	padding: "24px",
	backgroundColor: "#f9fafb",
	minHeight: "100vh",
});

const headerStyle = css({
	marginBottom: "32px",
});

const titleStyle = css({
	fontSize: "24px",
	fontWeight: "600",
	color: "#111827",
	marginBottom: "4px",
});

const mainContentStyle = grid({
	columns: 2,
	gap: "24px",
	marginBottom: "32px",
});

const cardStyle = css({
	backgroundColor: "white",
	borderRadius: "8px",
	border: "1px solid #e5e7eb",
	overflow: "hidden",
});

const cardHeaderStyle = css({
	padding: "16px 20px",
	borderBottom: "1px solid #e5e7eb",
	fontSize: "16px",
	fontWeight: "600",
	color: "#111827",
});

const uploadAreaStyle = css({
	padding: "40px 20px",
	textAlign: "center",
	border: "2px dashed #d1d5db",
	margin: "20px",
	borderRadius: "8px",
	backgroundColor: "#f9fafb",
	cursor: "pointer",
	transition: "all 0.2s",
	"&:hover": {
		borderColor: "#6366f1",
		backgroundColor: "#f3f4f6",
	},
});

const uploadedImageStyle = css({
	padding: "20px",
	textAlign: "center",
});

const imageStyle = css({
	maxWidth: "100%",
	maxHeight: "300px",
	borderRadius: "8px",
	border: "1px solid #e5e7eb",
});

const transparentImageStyle = css({
	maxWidth: "100%",
	maxHeight: "300px",
	borderRadius: "8px",
	border: "1px solid #e5e7eb",
	background: `
		linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
		linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
		linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
		linear-gradient(-45deg, transparent 75%, #f0f0f0 75%)
	`,
	backgroundSize: "20px 20px",
	backgroundPosition: "0 0, 0 10px, 10px -10px, -10px 0px",
});

const uploadIconStyle = css({
	color: "#6b7280",
	marginBottom: "12px",
});

const uploadTextStyle = css({
	fontSize: "16px",
	fontWeight: "500",
	color: "#374151",
	marginBottom: "4px",
});

const uploadSubtextStyle = css({
	fontSize: "14px",
	color: "#6b7280",
});

const resultPlaceholderStyle = css({
	padding: "80px 20px",
	textAlign: "center",
	color: "#9ca3af",
});

const toolsHeaderStyle = css({
	fontSize: "18px",
	fontWeight: "600",
	color: "#111827",
	marginBottom: "16px",
});

const toolsGridStyle = grid({
	columns: 2,
	gap: "16px",
});

const toolCardStyle = css({
	backgroundColor: "white",
	borderRadius: "8px",
	border: "1px solid #e5e7eb",
	padding: "20px",
	cursor: "pointer",
	transition: "all 0.2s",
	"&:hover": {
		borderColor: "#6366f1",
		transform: "translateY(-2px)",
		boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
	},
});

const toolTitleStyle = css({
	fontSize: "16px",
	fontWeight: "600",
	color: "#111827",
	marginBottom: "4px",
});

const toolDescriptionStyle = css({
	fontSize: "14px",
	color: "#6b7280",
});

const successMessageStyle = css({
	backgroundColor: "#d1fae5",
	color: "#065f46",
	padding: "12px 16px",
	borderRadius: "6px",
	fontSize: "14px",
	margin: "16px 20px",
});



interface ProcessingResult {
	success: boolean;
	message: string;
	output_data?: string;
}

// Helper function to check if we're running in Tauri context
const isTauriContext = () => {
	// For Tauri v2, check multiple indicators
	return typeof window !== 'undefined' && (
		(window as any).__TAURI__ !== undefined ||
		(window as any).__TAURI_INTERNALS__ !== undefined ||
		typeof invoke !== 'undefined'
	);
};

const ImageProcessingStudio = () => {
	const [uploadedImage, setUploadedImage] = useState<string | null>(null);
	const [isUploaded, setIsUploaded] = useState(false);
	const [processedImage, setProcessedImage] = useState<string | null>(null);
	const [isProcessing, setIsProcessing] = useState(false);
	const [processingMessage, setProcessingMessage] = useState<string>("");
	const [selectedTool, setSelectedTool] = useState<string | null>(null);

	const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0];
		if (file) {
			const reader = new FileReader();
			reader.onload = (e) => {
				setUploadedImage(e.target?.result as string);
				setIsUploaded(true);
			};
			reader.readAsDataURL(file);
		}
	};

	const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
		event.preventDefault();
		const file = event.dataTransfer.files[0];
		if (file && file.type.startsWith('image/')) {
			const reader = new FileReader();
			reader.onload = (e) => {
				setUploadedImage(e.target?.result as string);
				setIsUploaded(true);
			};
			reader.readAsDataURL(file);
		}
	};

	const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
		event.preventDefault();
	};

	const handleRemoveBackground = async () => {
		if (!uploadedImage) {
			setProcessingMessage("Please upload an image first");
			return;
		}

		setIsProcessing(true);
		setSelectedTool("remove-background");
		setProcessingMessage("Removing background...");
		setProcessedImage(null);

		try {
			console.log('Starting background removal...');
			console.log('Input image data length:', uploadedImage.length);
			console.log('Input image starts with:', uploadedImage.substring(0, 50));

			// Debug Tauri context
			console.log('window.__TAURI__:', (window as any).__TAURI__);
			console.log('window.__TAURI_INTERNALS__:', (window as any).__TAURI_INTERNALS__);
			console.log('typeof invoke:', typeof invoke);
			console.log('isTauriContext():', isTauriContext());
			console.log('typeof window:', typeof window);

			console.log('Attempting to call Tauri backend directly...');

			// Try to call Tauri backend directly, regardless of context detection
			try {
				const testResult = await invoke<string>("test_connection");
				console.log('Tauri connection test successful:', testResult);
			} catch (testError) {
				console.error('Tauri connection test failed:', testError);

				// If Tauri call fails, fall back to development mode
				if (!isTauriContext()) {
					setProcessingMessage("Development mode: Simulating background removal...");
					await new Promise(resolve => setTimeout(resolve, 2000));
					setProcessedImage(uploadedImage);
					setProcessingMessage("Demo mode: Background removal simulated (run 'npm run tauri dev' for real processing)");
					return;
				} else {
					setProcessingMessage("Error: Cannot connect to Tauri backend");
					return;
				}
			}

			try {
				const result = await invoke<ProcessingResult>("remove_background", {
					imageData: uploadedImage
				});

				console.log('Background removal result:', {
					success: result.success,
					message: result.message,
					hasOutputData: !!result.output_data,
					outputDataLength: result.output_data?.length || 0
				});

				if (result.success && result.output_data) {
					console.log('Output image starts with:', result.output_data.substring(0, 50));

					// Check if the output is actually different from input
					if (result.output_data === uploadedImage) {
						console.warn('Output image is identical to input image!');
						setProcessingMessage("Warning: Output appears identical to input. Background removal may have failed.");
					} else if (result.output_data.length === uploadedImage.length) {
						console.warn('Output image has same length as input - may be identical');
						setProcessingMessage("Background removed (processing complete)");
					} else {
						console.log('Images are different - background removal appears successful');
						setProcessingMessage("Background removed successfully!");
					}

					setProcessedImage(result.output_data);
				} else {
					console.error('Background removal failed:', result.message);
					setProcessingMessage(result.message || "Failed to remove background");
				}
			} catch (invokeError) {
				console.error('Tauri invoke error:', invokeError);
				throw invokeError; // Re-throw to be caught by outer catch
			}
		} catch (error) {
			console.error("Error removing background:", error);
			if (!isTauriContext()) {
				setProcessingMessage("Development mode: Please run 'npm run tauri dev' to test background removal");
			} else {
				setProcessingMessage("Error: " + String(error));
			}
		} finally {
			setIsProcessing(false);
		}
	};

	const handleDownload = () => {
		if (!processedImage) {
			console.error('No processed image to download');
			return;
		}

		console.log('Starting download...');
		console.log('Processed image starts with:', processedImage.substring(0, 50));

		try {
			// Extract the base64 data and convert to blob
			const base64Data = processedImage.split(',')[1]; // Remove data:image/png;base64, prefix
			const byteCharacters = atob(base64Data);
			const byteNumbers = new Array(byteCharacters.length);

			for (let i = 0; i < byteCharacters.length; i++) {
				byteNumbers[i] = byteCharacters.charCodeAt(i);
			}

			const byteArray = new Uint8Array(byteNumbers);
			const blob = new Blob([byteArray], { type: 'image/png' });

			console.log('Blob created, size:', blob.size);

			// Create download link
			const url = URL.createObjectURL(blob);
			const link = document.createElement('a');
			link.href = url;
			link.download = selectedTool === "remove-background" ? 'image-no-bg.png' : 'processed-image.png';

			// Trigger download
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);

			// Clean up
			URL.revokeObjectURL(url);

			console.log('Download completed successfully');
		} catch (error) {
			console.error('Download failed:', error);

			// Fallback: try direct download with data URL
			try {
				const link = document.createElement('a');
				link.href = processedImage;
				link.download = selectedTool === "remove-background" ? 'image-no-bg.png' : 'processed-image.png';
				document.body.appendChild(link);
				link.click();
				document.body.removeChild(link);
				console.log('Fallback download attempted');
			} catch (fallbackError) {
				console.error('Fallback download also failed:', fallbackError);
				alert('Download failed. Please try again or contact support.');
			}
		}
	};

	const handleReset = () => {
		setProcessedImage(null);
		setSelectedTool(null);
		setProcessingMessage("");
	};

	return (
		<div className={containerStyle}>
			<div className={headerStyle}>
				<h1 className={titleStyle}>Image Processing Studio</h1>
				<p className={css({ fontSize: "14px", color: "#6b7280" })}>Upload an image and choose your processing tool</p>
				{!isTauriContext() && (
					<div className={css({
						backgroundColor: "#fef3c7",
						color: "#92400e",
						padding: "8px 12px",
						borderRadius: "6px",
						fontSize: "12px",
						marginTop: "8px",
						border: "1px solid #fbbf24"
					})}>
						⚠️ Development Mode: Run <code>npm run tauri dev</code> for full functionality
					</div>
				)}
			</div>

			<div className={mainContentStyle}>
				{/* Upload Image Section */}
				<div className={cardStyle}>
					<div className={cardHeaderStyle}>Upload Image</div>
					{isUploaded && uploadedImage ? (
						<>
							<div className={successMessageStyle}>
								Image uploaded successfully. Choose a processing tool below.
							</div>
							<div className={uploadedImageStyle}>
								<img src={uploadedImage} alt="Uploaded" className={imageStyle} />
								<div style={{ marginTop: '16px' }}>
									<button
										onClick={() => document.getElementById('file-input')?.click()}
										className={css({
											backgroundColor: "#f3f4f6",
											color: "#374151",
											padding: "8px 16px",
											borderRadius: "6px",
											border: "1px solid #d1d5db",
											cursor: "pointer",
											fontSize: "14px",
											fontWeight: "500",
											"&:hover": {
												backgroundColor: "#e5e7eb"
											}
										})}
									>
										<TbUpload size={16} style={{ marginRight: '8px', display: 'inline' }} />
										Upload New Image
									</button>
								</div>
							</div>
							<input
								id="file-input"
								type="file"
								accept="image/*"
								onChange={handleImageUpload}
								style={{ display: 'none' }}
							/>
						</>
					) : (
						<div
							className={uploadAreaStyle}
							onDrop={handleDrop}
							onDragOver={handleDragOver}
							onClick={() => document.getElementById('file-input')?.click()}
						>
							<TbUpload size={48} className={uploadIconStyle} />
							<div className={uploadTextStyle}>Click to upload or drag and drop</div>
							<div className={uploadSubtextStyle}>PNG, JPG, GIF up to 10MB</div>
							<input
								id="file-input"
								type="file"
								accept="image/*"
								onChange={handleImageUpload}
								style={{ display: 'none' }}
							/>
						</div>
					)}
				</div>

				{/* Processed Result Section */}
				<div className={cardStyle}>
					<div className={cardHeaderStyle}>Processed Result</div>
					{isProcessing ? (
						<div className={resultPlaceholderStyle}>
							<TbLoader2 size={48} style={{ marginBottom: '12px', animation: 'spin 1s linear infinite' }} />
							<div>{processingMessage}</div>
						</div>
					) : processedImage ? (
						<>
							<div className={successMessageStyle}>
								{processingMessage}
							</div>
							{selectedTool === "remove-background" && uploadedImage ? (
								// Show before/after comparison for background removal
								<div className={css({ padding: "20px" })}>
									<div className={css({
										display: "grid",
										gridTemplateColumns: "1fr 1fr",
										gap: "16px",
										marginBottom: "16px"
									})}>
										<div>
											<div className={css({
												fontSize: "14px",
												fontWeight: "600",
												marginBottom: "8px",
												textAlign: "center"
											})}>
												Before
											</div>
											<img src={uploadedImage} alt="Original" className={imageStyle} />
										</div>
										<div>
											<div className={css({
												fontSize: "14px",
												fontWeight: "600",
												marginBottom: "8px",
												textAlign: "center"
											})}>
												After
											</div>
											<img
												src={processedImage}
												alt="Processed"
												className={transparentImageStyle}
											/>
										</div>
									</div>
									<div className={css({
										fontSize: "12px",
										color: "#6b7280",
										textAlign: "center",
										fontStyle: "italic",
										marginBottom: "16px"
									})}>
										✨ Background removed - checkered pattern shows transparency
									</div>
								</div>
							) : (
								// Show single processed image for other tools
								<div className={uploadedImageStyle}>
									<img
										src={processedImage}
										alt="Processed"
										className={imageStyle}
									/>
								</div>
							)}
							<div style={{ marginTop: '16px', textAlign: 'center' }}>
								<button
									onClick={handleDownload}
									className={css({
										backgroundColor: "#6366f1",
										color: "white",
										padding: "8px 16px",
										borderRadius: "6px",
										border: "none",
										cursor: "pointer",
										fontSize: "14px",
										fontWeight: "500",
										marginRight: "8px",
										"&:hover": {
											backgroundColor: "#5856eb"
										}
									})}
								>
									<TbDownload size={16} style={{ marginRight: '8px', display: 'inline' }} />
									Download {selectedTool === "remove-background" ? "PNG" : "Result"}
								</button>
								{selectedTool === "remove-background" && (
									<button
										onClick={() => {
											// Toggle background preview
											const img = document.querySelector('img[alt="Processed"]') as HTMLImageElement;
											if (img) {
												const currentBg = img.style.backgroundColor;
												img.style.backgroundColor = currentBg === 'white' ? 'black' : 'white';
											}
										}}
										className={css({
											backgroundColor: "#f3f4f6",
											color: "#374151",
											padding: "8px 16px",
											borderRadius: "6px",
											border: "1px solid #d1d5db",
											cursor: "pointer",
											fontSize: "14px",
											fontWeight: "500",
											marginRight: "8px",
											"&:hover": {
												backgroundColor: "#e5e7eb"
											}
										})}
									>
										Toggle Background
									</button>
								)}
								<button
									onClick={handleReset}
									className={css({
										backgroundColor: "#f9fafb",
										color: "#6b7280",
										padding: "8px 16px",
										borderRadius: "6px",
										border: "1px solid #d1d5db",
										cursor: "pointer",
										fontSize: "14px",
										fontWeight: "500",
										"&:hover": {
											backgroundColor: "#f3f4f6"
										}
									})}
								>
									Try Another Tool
								</button>
							</div>
						</>
					) : (
						<div className={resultPlaceholderStyle}>
							<TbDownload size={48} style={{ marginBottom: '12px' }} />
							<div>Upload an image and select a tool to see results here</div>
						</div>
					)}
				</div>
			</div>

			{/* Processing Tools Section */}
			<div>
				<h2 className={toolsHeaderStyle}>Choose Processing Tool</h2>
				<div className={toolsGridStyle}>
					<div className={toolCardStyle}>
						<div className={hstack({ gap: "0" })}>
							<div className={css({ backgroundColor: "#10b981", borderRadius: "8px", padding: "8px", marginRight: "12px" })}>
								<TbSparkles size={24} className={css({ color: "white" })} />
							</div>
							<div>
								<div className={toolTitleStyle}>Image Enhancement</div>
								<div className={toolDescriptionStyle}>Improve quality, brightness, and contrast</div>
							</div>
						</div>
					</div>

					<div className={toolCardStyle}>
						<div className={hstack({ gap: "0" })}>
							<div className={css({ backgroundColor: "#3b82f6", borderRadius: "8px", padding: "8px", marginRight: "12px" })}>
								<TbArrowUp size={24} className={css({ color: "white" })} />
							</div>
							<div>
								<div className={toolTitleStyle}>AI Upscale</div>
								<div className={toolDescriptionStyle}>Increase resolution using AI technology</div>
							</div>
						</div>
					</div>

					<div
						className={cx(
							toolCardStyle,
							selectedTool === "remove-background" ? css({ borderColor: "#8b5cf6", backgroundColor: "#faf5ff" }) : "",
							!uploadedImage ? css({ opacity: 0.5, cursor: "not-allowed" }) : ""
						)}
						onClick={uploadedImage && !isProcessing ? handleRemoveBackground : undefined}
					>
						<div className={hstack({ gap: "0" })}>
							<div className={css({ backgroundColor: "#8b5cf6", borderRadius: "8px", padding: "8px", marginRight: "12px" })}>
								{isProcessing && selectedTool === "remove-background" ? (
									<TbLoader2 size={24} className={css({ color: "white", animation: "spin 1s linear infinite" })} />
								) : (
									<TbBackground size={24} className={css({ color: "white" })} />
								)}
							</div>
							<div>
								<div className={toolTitleStyle}>Remove Background</div>
								<div className={toolDescriptionStyle}>
									{isProcessing && selectedTool === "remove-background"
										? "Processing..."
										: "Automatically remove image background"
									}
								</div>
							</div>
						</div>
					</div>

					<div className={toolCardStyle}>
						<div className={hstack({ gap: "0" })}>
							<div className={css({ backgroundColor: "#f59e0b", borderRadius: "8px", padding: "8px", marginRight: "12px" })}>
								<TbDroplet size={24} className={css({ color: "white" })} />
							</div>
							<div>
								<div className={toolTitleStyle}>Remove Watermark</div>
								<div className={toolDescriptionStyle}>Intelligently remove watermarks and text</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default ImageProcessingStudio;
