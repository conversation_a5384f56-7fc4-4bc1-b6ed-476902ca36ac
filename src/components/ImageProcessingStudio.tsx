import { useState } from "react";
import { TbUpload, TbDownload, Tb<PERSON><PERSON><PERSON>, TbArrowUp, TbBackground, TbDroplet, TbLoader2 } from "react-icons/tb";
import { css, cx } from "styled-system/css";
import { hstack, grid } from "styled-system/patterns";
import { invoke } from "@tauri-apps/api/core";

const containerStyle = css({
	padding: "24px",
	backgroundColor: "#f9fafb",
	minHeight: "100vh",
});

const headerStyle = css({
	marginBottom: "32px",
});

const titleStyle = css({
	fontSize: "24px",
	fontWeight: "600",
	color: "#111827",
	marginBottom: "4px",
});

const mainContentStyle = grid({
	columns: 2,
	gap: "24px",
	marginBottom: "32px",
});

const cardStyle = css({
	backgroundColor: "white",
	borderRadius: "8px",
	border: "1px solid #e5e7eb",
	overflow: "hidden",
});

const cardHeaderStyle = css({
	padding: "16px 20px",
	borderBottom: "1px solid #e5e7eb",
	fontSize: "16px",
	fontWeight: "600",
	color: "#111827",
});

const uploadAreaStyle = css({
	padding: "40px 20px",
	textAlign: "center",
	border: "2px dashed #d1d5db",
	margin: "20px",
	borderRadius: "8px",
	backgroundColor: "#f9fafb",
	cursor: "pointer",
	transition: "all 0.2s",
	"&:hover": {
		borderColor: "#6366f1",
		backgroundColor: "#f3f4f6",
	},
});

const uploadedImageStyle = css({
	padding: "20px",
	textAlign: "center",
});

const imageStyle = css({
	maxWidth: "100%",
	maxHeight: "300px",
	borderRadius: "8px",
	border: "1px solid #e5e7eb",
});

const uploadIconStyle = css({
	color: "#6b7280",
	marginBottom: "12px",
});

const uploadTextStyle = css({
	fontSize: "16px",
	fontWeight: "500",
	color: "#374151",
	marginBottom: "4px",
});

const uploadSubtextStyle = css({
	fontSize: "14px",
	color: "#6b7280",
});

const resultPlaceholderStyle = css({
	padding: "80px 20px",
	textAlign: "center",
	color: "#9ca3af",
});

const toolsHeaderStyle = css({
	fontSize: "18px",
	fontWeight: "600",
	color: "#111827",
	marginBottom: "16px",
});

const toolsGridStyle = grid({
	columns: 2,
	gap: "16px",
});

const toolCardStyle = css({
	backgroundColor: "white",
	borderRadius: "8px",
	border: "1px solid #e5e7eb",
	padding: "20px",
	cursor: "pointer",
	transition: "all 0.2s",
	"&:hover": {
		borderColor: "#6366f1",
		transform: "translateY(-2px)",
		boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
	},
});

const toolTitleStyle = css({
	fontSize: "16px",
	fontWeight: "600",
	color: "#111827",
	marginBottom: "4px",
});

const toolDescriptionStyle = css({
	fontSize: "14px",
	color: "#6b7280",
});

const successMessageStyle = css({
	backgroundColor: "#d1fae5",
	color: "#065f46",
	padding: "12px 16px",
	borderRadius: "6px",
	fontSize: "14px",
	margin: "16px 20px",
});



interface ProcessingResult {
	success: boolean;
	message: string;
	output_data?: string;
}

// Helper function to check if we're running in Tauri context
const isTauriContext = () => {
	return typeof window !== 'undefined' && (window as any).__TAURI__ !== undefined;
};

const ImageProcessingStudio = () => {
	const [uploadedImage, setUploadedImage] = useState<string | null>(null);
	const [isUploaded, setIsUploaded] = useState(false);
	const [processedImage, setProcessedImage] = useState<string | null>(null);
	const [isProcessing, setIsProcessing] = useState(false);
	const [processingMessage, setProcessingMessage] = useState<string>("");
	const [selectedTool, setSelectedTool] = useState<string | null>(null);

	const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0];
		if (file) {
			const reader = new FileReader();
			reader.onload = (e) => {
				setUploadedImage(e.target?.result as string);
				setIsUploaded(true);
			};
			reader.readAsDataURL(file);
		}
	};

	const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
		event.preventDefault();
		const file = event.dataTransfer.files[0];
		if (file && file.type.startsWith('image/')) {
			const reader = new FileReader();
			reader.onload = (e) => {
				setUploadedImage(e.target?.result as string);
				setIsUploaded(true);
			};
			reader.readAsDataURL(file);
		}
	};

	const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
		event.preventDefault();
	};

	const handleRemoveBackground = async () => {
		if (!uploadedImage) {
			setProcessingMessage("Please upload an image first");
			return;
		}

		setIsProcessing(true);
		setSelectedTool("remove-background");
		setProcessingMessage("Removing background...");
		setProcessedImage(null);

		try {
			// Check if we're running in Tauri context
			if (!isTauriContext()) {
				// Development mode - simulate the process
				setProcessingMessage("Development mode: Simulating background removal...");

				// Simulate processing time
				await new Promise(resolve => setTimeout(resolve, 2000));

				// For demo purposes, just return the original image with a message
				setProcessedImage(uploadedImage);
				setProcessingMessage("Demo mode: Background removal simulated (run 'npm run tauri dev' for real processing)");
				return;
			}

			const result = await invoke<ProcessingResult>("remove_background", {
				imageData: uploadedImage
			});

			if (result.success && result.output_data) {
				setProcessedImage(result.output_data);
				setProcessingMessage("Background removed successfully!");
			} else {
				setProcessingMessage(result.message || "Failed to remove background");
			}
		} catch (error) {
			console.error("Error removing background:", error);
			if (!isTauriContext()) {
				setProcessingMessage("Development mode: Please run 'npm run tauri dev' to test background removal");
			} else {
				setProcessingMessage("Error: " + String(error));
			}
		} finally {
			setIsProcessing(false);
		}
	};

	const handleDownload = () => {
		if (!processedImage) return;

		const link = document.createElement('a');
		link.href = processedImage;
		link.download = 'processed-image.png';
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	};

	return (
		<div className={containerStyle}>
			<div className={headerStyle}>
				<h1 className={titleStyle}>Image Processing Studio</h1>
			</div>

			<div className={mainContentStyle}>
				{/* Upload Image Section */}
				<div className={cardStyle}>
					<div className={cardHeaderStyle}>Upload Image</div>
					{isUploaded && uploadedImage ? (
						<>
							<div className={successMessageStyle}>
								Image uploaded successfully. Choose a processing tool below.
							</div>
							<div className={uploadedImageStyle}>
								<img src={uploadedImage} alt="Uploaded" className={imageStyle} />
							</div>
						</>
					) : (
						<div
							className={uploadAreaStyle}
							onDrop={handleDrop}
							onDragOver={handleDragOver}
							onClick={() => document.getElementById('file-input')?.click()}
						>
							<TbUpload size={48} className={uploadIconStyle} />
							<div className={uploadTextStyle}>Click to upload or drag and drop</div>
							<div className={uploadSubtextStyle}>PNG, JPG, GIF up to 10MB</div>
							<input
								id="file-input"
								type="file"
								accept="image/*"
								onChange={handleImageUpload}
								style={{ display: 'none' }}
							/>
						</div>
					)}
				</div>

				{/* Processed Result Section */}
				<div className={cardStyle}>
					<div className={cardHeaderStyle}>Processed Result</div>
					{isProcessing ? (
						<div className={resultPlaceholderStyle}>
							<TbLoader2 size={48} style={{ marginBottom: '12px', animation: 'spin 1s linear infinite' }} />
							<div>{processingMessage}</div>
						</div>
					) : processedImage ? (
						<>
							<div className={successMessageStyle}>
								{processingMessage}
							</div>
							<div className={uploadedImageStyle}>
								<img src={processedImage} alt="Processed" className={imageStyle} />
								<div style={{ marginTop: '16px' }}>
									<button
										onClick={handleDownload}
										className={css({
											backgroundColor: "#6366f1",
											color: "white",
											padding: "8px 16px",
											borderRadius: "6px",
											border: "none",
											cursor: "pointer",
											fontSize: "14px",
											fontWeight: "500",
											"&:hover": {
												backgroundColor: "#5856eb"
											}
										})}
									>
										<TbDownload size={16} style={{ marginRight: '8px', display: 'inline' }} />
										Download Result
									</button>
								</div>
							</div>
						</>
					) : (
						<div className={resultPlaceholderStyle}>
							<TbDownload size={48} style={{ marginBottom: '12px' }} />
							<div>Upload an image and select a tool to see results here</div>
						</div>
					)}
				</div>
			</div>

			{/* Processing Tools Section */}
			<div>
				<h2 className={toolsHeaderStyle}>Choose Processing Tool</h2>
				<div className={toolsGridStyle}>
					<div className={toolCardStyle}>
						<div className={hstack({ gap: "0" })}>
							<div className={css({ backgroundColor: "#10b981", borderRadius: "8px", padding: "8px", marginRight: "12px" })}>
								<TbSparkles size={24} className={css({ color: "white" })} />
							</div>
							<div>
								<div className={toolTitleStyle}>Image Enhancement</div>
								<div className={toolDescriptionStyle}>Improve quality, brightness, and contrast</div>
							</div>
						</div>
					</div>

					<div className={toolCardStyle}>
						<div className={hstack({ gap: "0" })}>
							<div className={css({ backgroundColor: "#3b82f6", borderRadius: "8px", padding: "8px", marginRight: "12px" })}>
								<TbArrowUp size={24} className={css({ color: "white" })} />
							</div>
							<div>
								<div className={toolTitleStyle}>AI Upscale</div>
								<div className={toolDescriptionStyle}>Increase resolution using AI technology</div>
							</div>
						</div>
					</div>

					<div
						className={cx(
							toolCardStyle,
							selectedTool === "remove-background" ? css({ borderColor: "#8b5cf6", backgroundColor: "#faf5ff" }) : "",
							!uploadedImage ? css({ opacity: 0.5, cursor: "not-allowed" }) : ""
						)}
						onClick={uploadedImage && !isProcessing ? handleRemoveBackground : undefined}
					>
						<div className={hstack({ gap: "0" })}>
							<div className={css({ backgroundColor: "#8b5cf6", borderRadius: "8px", padding: "8px", marginRight: "12px" })}>
								{isProcessing && selectedTool === "remove-background" ? (
									<TbLoader2 size={24} className={css({ color: "white", animation: "spin 1s linear infinite" })} />
								) : (
									<TbBackground size={24} className={css({ color: "white" })} />
								)}
							</div>
							<div>
								<div className={toolTitleStyle}>Remove Background</div>
								<div className={toolDescriptionStyle}>
									{isProcessing && selectedTool === "remove-background"
										? "Processing..."
										: "Automatically remove image background"
									}
								</div>
							</div>
						</div>
					</div>

					<div className={toolCardStyle}>
						<div className={hstack({ gap: "0" })}>
							<div className={css({ backgroundColor: "#f59e0b", borderRadius: "8px", padding: "8px", marginRight: "12px" })}>
								<TbDroplet size={24} className={css({ color: "white" })} />
							</div>
							<div>
								<div className={toolTitleStyle}>Remove Watermark</div>
								<div className={toolDescriptionStyle}>Intelligently remove watermarks and text</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default ImageProcessingStudio;
